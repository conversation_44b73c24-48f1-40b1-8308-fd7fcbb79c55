package dev.pigmomo.yhkit2025.ui.dialog

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import coil.compose.AsyncImage
import coil.request.ImageRequest
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.product.MainImage
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailData
import dev.pigmomo.yhkit2025.ui.theme.DialogContainerColor
import dev.pigmomo.yhkit2025.ui.theme.RoyalBlue
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor

/**
 * 商品详情弹窗
 *
 * @param productData 商品详情数据
 * @param cartItems 购物车数据列表
 * @param onDismiss 关闭弹窗回调
 * @param onAddToCart 添加到购物车回调
 * @param onAddFromCart 从购物车增加商品回调
 * @param onReduceFromCart 从购物车减少商品回调
 * @param onImageClick 图片点击回调（用于查看大图）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProductDetailDialog(
    productData: ProductDetailData,
    cartItems: List<CartItem> = emptyList(),
    onDismiss: () -> Unit,
    onAddToCart: ((String) -> Unit)? = null,
    onAddFromCart: (Product) -> Unit = {},
    onReduceFromCart: (Product) -> Unit = {},
    onImageClick: ((String) -> Unit)? = null
) {
    var selectedImageIndex by remember { mutableIntStateOf(0) }

    // 查找商品在购物车中的数量和Product对象
    val cartProductInfo = remember(cartItems, productData.id) {
        findProductInCart(productData.id ?: "", cartItems)
    }
    val cartQuantity = cartProductInfo?.first ?: 0
    val cartProduct = cartProductInfo?.second

    Log.d("ProductDetailDialog", "productData.id: ${productData.id}")
    Log.d("ProductDetailDialog", "cartQuantity: $cartQuantity")
    Log.d("ProductDetailDialog", "cartProduct: $cartProduct")

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("商品详情") },
        containerColor = dialogContainerColor(),
        confirmButton = { },
        dismissButton = null,
        text = {
            Card(
                modifier = Modifier
                    .heightIn(max = 500.dp),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(containerColor = DialogContainerColor)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    // 内容区域
                    LazyColumn(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 商品图片轮播
                        item {
                            ProductImageCarousel(
                                images = productData.mainimgs ?: emptyList(),
                                selectedIndex = selectedImageIndex,
                                onImageSelected = { selectedImageIndex = it },
                                onImageClick = onImageClick
                            )
                        }

                        // 商品基本信息
                        item {
                            ProductBasicInfo(
                                productData = productData,
                                cartQuantity = cartQuantity,
                                cartProduct = cartProduct,
                                onAddToCart = {
                                    onAddToCart?.invoke(productData.id ?: "")
                                },
                                onAddFromCart = { product ->
                                    onAddFromCart(product)
                                },
                                onReduceFromCart = { product ->
                                    onReduceFromCart(product)
                                }
                            )
                        }

                        // 价格、库存信息
                        item {
                            ProductPriceInfo(productData)
                        }

                        // 商品属性
                        item {
                            ProductAttributes(productData)
                        }

                        // 评论信息
                        item {
                            ProductCommentInfo(productData)
                        }


                        // 卖家信息
                        item {
                            ProductSellerInfo(productData)
                        }
                    }
                }
            }
        })
}

/**
 * 商品图片轮播组件
 */
@Composable
private fun ProductImageCarousel(
    images: List<MainImage>,
    selectedIndex: Int,
    onImageSelected: (Int) -> Unit,
    onImageClick: ((String) -> Unit)?
) {
    Column {
        // 主图片
        if (images.isNotEmpty()) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(images[selectedIndex].imgurl)
                    .crossfade(true)
                    .build(),
                contentDescription = "商品图片",
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f)
                    .clip(RoundedCornerShape(8.dp))
                    .clickable {
                        onImageClick?.invoke(images[selectedIndex].imgurl ?: "")
                    },
                contentScale = ContentScale.Crop
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        // 图片缩略图
        if (images.size > 1) {
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(images.size) { index ->
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(images[index].imgurl)
                            .crossfade(true)
                            .build(),
                        contentDescription = "缩略图",
                        modifier = Modifier
                            .size(60.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .border(
                                width = if (index == selectedIndex) 2.dp else 0.dp,
                                color = if (index == selectedIndex) RoyalBlue else Color.Transparent,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .clickable { onImageSelected(index) },
                        contentScale = ContentScale.Crop
                    )
                }
            }
        }
    }
}

/**
 * 商品基本信息组件（包含加购与加减操作）
 */
@Composable
private fun ProductBasicInfo(
    productData: ProductDetailData,
    cartQuantity: Int = 0,
    cartProduct: Product? = null,
    onAddToCart: () -> Unit = {},
    onAddFromCart: (Product) -> Unit = {},
    onReduceFromCart: (Product) -> Unit = {}
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 商品标题
            Text(
                text = productData.title ?: "未知商品",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                maxLines = 2,
                modifier = Modifier.horizontalScroll(rememberScrollState())
            )

            // 促销信息（横向滑动）
            productData.promotion?.let { promotion ->
                if (!promotion.promonames.isNullOrEmpty() || !promotion.coupons.isNullOrEmpty()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 促销活动
                        if (!promotion.promonames.isNullOrEmpty()) {
                            item {
                                Surface(
                                    color = Color(0xFFFFEBEE),
                                    shape = RoundedCornerShape(4.dp)
                                ) {
                                    Text(
                                        text = promotion.promonames,
                                        fontSize = 10.sp,
                                        color = Color.Red,
                                        modifier = Modifier.padding(
                                            horizontal = 6.dp,
                                            vertical = 2.dp
                                        )
                                    )
                                }
                            }
                        }

                        // 优惠券
                        promotion.coupons?.take(3)?.let { coupons ->
                            items(coupons) { coupon ->
                                Surface(
                                    color = Color(0xFFFFF3E0),
                                    shape = RoundedCornerShape(4.dp)
                                ) {
                                    Text(
                                        text = coupon.showDescription ?: coupon.name ?: "",
                                        fontSize = 10.sp,
                                        color = Color(0xFFFF9800),
                                        modifier = Modifier.padding(
                                            horizontal = 6.dp,
                                            vertical = 2.dp
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }

            // 商品副标题
            if (!productData.subtitle.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = productData.subtitle,
                    fontSize = 14.sp,
                    color = Color.Gray,
                    maxLines = 1,
                    modifier = Modifier.horizontalScroll(rememberScrollState())
                )
            }

            // 商品编码和操作按钮在同一行
            Spacer(modifier = Modifier.height(4.dp))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 4.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "商品编码: ${productData.id ?: "未知"}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                // 只有有库存的商品才显示按钮
                if ((productData.price?.canBuy == 1) && (productData.stock?.count ?: 0) > 0) {
                    // 根据购物车中的数量显示不同的UI
                    if (cartQuantity > 0) {
                        // 显示数量控制器（与购物车样式保持一致）
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 减少按钮
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .clip(RoundedCornerShape(2.dp))
                                    .background(Color(0xFFF5F5F5))
                                    .clickable {
                                        cartProduct?.let { onReduceFromCart(it) }
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "-",
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    color = Color.Black
                                )
                            }

                            // 数量显示
                            Box(
                                modifier = Modifier
                                    .width(24.dp)
                                    .height(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "${cartQuantity / 100}",
                                    lineHeight = 12.sp,
                                    fontSize = 12.sp
                                )
                            }

                            // 增加按钮
                            Box(
                                modifier = Modifier
                                    .size(16.dp)
                                    .clip(RoundedCornerShape(2.dp))
                                    .background(
                                        if ((productData.stock?.count
                                                ?: 0) > (cartQuantity / 100)
                                        ) Color(0xFFF5F5F5)
                                        else Color(0xFFE0E0E0)
                                    )
                                    .clickable(
                                        enabled = (productData.stock?.count
                                            ?: 0) > (cartQuantity / 100)
                                    ) {
                                        cartProduct?.let { onAddFromCart(it) }
                                    },
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "+",
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    color = if ((productData.stock?.count
                                            ?: 0) > (cartQuantity / 100)
                                    ) Color.Black else Color.Gray
                                )
                            }
                        }
                    } else {
                        // 显示加购按钮（与购物车样式保持一致）
                        Box(
                            modifier = Modifier
                                .height(16.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(Color(0xFFF5F5F5))
                                .clickable {
                                    onAddToCart()
                                }
                                .padding(horizontal = 8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "加入购物车",
                                fontSize = 12.sp,
                                lineHeight = 12.sp,
                                color = Color.Black
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 商品价格信息组件
 */
@SuppressLint("DefaultLocale")
@Composable
private fun ProductPriceInfo(productData: ProductDetailData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            productData.price?.let { price ->
                Row(
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 当前价格
                    Text(
                        text = "¥${String.format("%.2f", (price.value ?: 0) / 100.0)}",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Red
                    )

                    // 市场价
                    if ((price.market ?: 0) > (price.value ?: 0)) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "¥${String.format("%.2f", (price.market ?: 0) / 100.0)}",
                                fontSize = 14.sp,
                                color = Color.Gray,
                                textDecoration = TextDecoration.LineThrough
                            )

                            // 折扣
                            val discount =
                                ((price.market!! - price.value!!) * 100.0 / price.market).toInt()
                            if (discount > 0) {
                                Text(
                                    text = "${discount}%OFF",
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    color = Color.White,
                                    modifier = Modifier
                                        .background(
                                            Color.Red,
                                            RoundedCornerShape(4.dp)
                                        )
                                        .padding(horizontal = 4.dp)
                                )
                            }
                        }
                    }
                }

                // 库存和规格在同一行
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 库存信息
                    productData.stock?.let { stock ->
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 库存描述
                            if (!stock.desc.isNullOrEmpty()) {
                                Text(
                                    text = stock.desc,
                                    fontSize = 12.sp,
                                    color = Color.Gray,
                                    fontWeight = FontWeight.Medium
                                )
                            }

                            // 库存数量
                            Text(
                                text = "库存: ${(stock.count ?: 0) / 100}",
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    } ?: run {
                        // 如果没有库存信息，显示空的Box占位
                        Box {}
                    }

                    // 规格信息
                    if (!price.spec.isNullOrEmpty()) {
                        Text(
                            text = "规格: ${price.spec}",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }

                // 最小购买数量
                productData.stock?.let { stock ->
                    if ((stock.minNum ?: 0) > 0) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "最小购买数量: ${stock.minNum}",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
        }
    }
}


/**
 * 商品属性组件
 */
@Composable
private fun ProductAttributes(productData: ProductDetailData) {
    if (!productData.place.isNullOrEmpty()) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = "商品属性",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = RoyalBlue
                )

                Spacer(modifier = Modifier.height(8.dp))

                productData.place.forEach { place ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 2.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "${place.prompt}:",
                            fontSize = 13.sp,
                            color = Color.Gray,
                            modifier = Modifier.weight(1f)
                        )
                        Text(
                            text = place.value ?: "",
                            fontSize = 13.sp,
                            color = Color.Black,
                            modifier = Modifier.weight(2f)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 商品评论信息组件
 */
@Composable
private fun ProductCommentInfo(productData: ProductDetailData) {
    productData.comment?.let { comment ->
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = "用户评价",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = RoyalBlue
                )

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 评分星星
                    repeat(5) { index ->
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "星星",
                            tint = if (index < 5) Color(0xFFFFD700) else Color.Gray,
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    Text(
                        text = "${comment.realPositiveRate ?: 0.0}%",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Gray
                    )
                }

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "共 ${comment.count ?: 0} 条评价",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                // 评论标签
                comment.commentTagInfos?.take(3)?.let { tags ->
                    if (tags.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(tags) { tag ->
                                Surface(
                                    color = Color(0xFFF5F5F5),
                                    shape = RoundedCornerShape(12.dp)
                                ) {
                                    Text(
                                        text = "${tag.name} (${tag.count})",
                                        fontSize = 11.sp,
                                        color = Color.Gray,
                                        modifier = Modifier.padding(
                                            horizontal = 8.dp,
                                            vertical = 4.dp
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


/**
 * 卖家信息组件
 */
@Composable
private fun ProductSellerInfo(productData: ProductDetailData) {
    productData.seller?.let { seller ->
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 卖家图标
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(seller.icon)
                        .crossfade(true)
                        .build(),
                    contentDescription = "卖家图标",
                    modifier = Modifier
                        .size(24.dp)
                        .clip(RoundedCornerShape(20.dp)),
                    contentScale = ContentScale.Crop
                )

                Text(
                    text = seller.title ?: "未知卖家",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
            }
        }
    }
}

/**
 * 查找商品在购物车中的数量和Product对象
 * @param skuCode 商品SKU代码
 * @param cartItems 购物车数据列表
 * @return Pair<数量, Product对象> 如果找到，否则返回null
 */
private fun findProductInCart(skuCode: String, cartItems: List<CartItem>): Pair<Int, Product>? {
    if (skuCode.isEmpty()) return null

    cartItems.forEach { cartItem ->
        cartItem.cartModels.forEach { cartModel ->
            if (cartModel.modelType == CartModelTypes.PRODUCT_ITEM) {
                val cartModelWrapper = CartModelWrapper(
                    data = cartModel.data,
                    modelType = cartModel.modelType
                )
                val product = cartModelWrapper.getProduct()
                if (product != null) {
                    // 匹配商品ID或原始SKU代码
                    if (product.id == skuCode || product.originalskucode == skuCode) {
                        return Pair(product.num, product)
                    }
                }
            }
        }
    }
    return null
}
